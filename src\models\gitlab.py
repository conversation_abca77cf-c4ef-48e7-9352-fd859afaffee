"""GitLab相关数据模型"""

from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime


@dataclass
class GitLabMR:
    """GitLab合并请求基本信息"""
    project_path: str
    mr_id: int
    
    def __str__(self) -> str:
        return f"{self.project_path}!{self.mr_id}"


@dataclass
class GitLabCommit:
    """GitLab提交信息"""
    id: str
    short_id: str
    title: str
    message: str
    author_name: str
    author_email: str
    created_at: datetime
    web_url: str


@dataclass
class GitLabMergeRequest:
    """GitLab合并请求详细信息"""
    id: int
    iid: int
    project_id: int
    title: str
    description: str
    state: str
    created_at: datetime
    updated_at: datetime
    merged_at: Optional[datetime]
    closed_at: Optional[datetime]
    author: dict
    assignee: Optional[dict]
    source_branch: str
    target_branch: str
    web_url: str
    commits: List[GitLabCommit]
    
    @property
    def first_commit(self) -> Optional[GitLabCommit]:
        """获取第一个提交"""
        return self.commits[0] if self.commits else None
    
    @property
    def latest_commit(self) -> Optional[GitLabCommit]:
        """获取最新提交"""
        return self.commits[-1] if self.commits else None
