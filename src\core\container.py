"""依赖注入容器"""

import logging
from typing import Dict, Any, TypeVar, Type, Optional
from ..config import Settings
from ..services import FeishuService, GitLabService, ContentProcessor
from ..plugins import PluginManager, GitLabURLParserPlugin, DefaultContentExtractorPlugin

T = TypeVar('T')


class ServiceContainer:
    """服务容器 - 实现依赖注入"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._services: Dict[str, Any] = {}
        self._initialized = False
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def initialize(self) -> None:
        """初始化所有服务"""
        if self._initialized:
            return

        try:
            # 创建插件管理器
            plugin_manager = PluginManager(self.settings)

            # 注册默认插件
            plugin_manager.register_plugin(GitLabURLParserPlugin(self.settings))
            plugin_manager.register_plugin(DefaultContentExtractorPlugin(self.settings))

            # 初始化插件管理器
            await plugin_manager.initialize()
            self._services['plugin_manager'] = plugin_manager

            # 创建服务实例
            self._services['feishu'] = FeishuService(self.settings)
            self._services['gitlab'] = GitLabService(self.settings)
            self._services['content_processor'] = ContentProcessor(self.settings)

            # 为服务注入插件管理器
            self._services['gitlab'].plugin_manager = plugin_manager
            self._services['content_processor'].plugin_manager = plugin_manager

            # 初始化所有服务
            for name, service in self._services.items():
                if name != 'plugin_manager':  # 插件管理器已经初始化
                    await service.initialize()
                    self.logger.info(f"服务 {name} 初始化成功")

            self._initialized = True
            self.logger.info("所有服务初始化完成")

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            await self.cleanup()
            raise
    
    async def cleanup(self) -> None:
        """清理所有服务"""
        for name, service in self._services.items():
            try:
                await service.cleanup()
                self.logger.info(f"服务 {name} 清理完成")
            except Exception as e:
                self.logger.error(f"服务 {name} 清理失败: {e}")
        
        self._services.clear()
        self._initialized = False
        self.logger.info("所有服务清理完成")
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        if not self._initialized:
            raise RuntimeError("服务容器未初始化")
        
        service_name = service_type.__name__.lower().replace('service', '')
        if service_name == 'contentprocessor':
            service_name = 'content_processor'
        
        service = self._services.get(service_name)
        if service is None:
            raise ValueError(f"未找到服务: {service_type.__name__}")
        
        return service
    
    @property
    def feishu_service(self) -> FeishuService:
        """获取飞书服务"""
        return self.get_service(FeishuService)
    
    @property
    def gitlab_service(self) -> GitLabService:
        """获取GitLab服务"""
        return self.get_service(GitLabService)
    
    @property
    def content_processor(self) -> ContentProcessor:
        """获取内容处理器"""
        return self.get_service(ContentProcessor)

    @property
    def plugin_manager(self) -> PluginManager:
        """获取插件管理器"""
        return self._services.get('plugin_manager')
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
