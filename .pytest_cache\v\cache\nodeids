["tests/test_config.py::TestSettings::test_from_env_with_values", "tests/test_config.py::TestSettings::test_from_file_yaml", "tests/test_config.py::TestSettings::test_validate_invalid_polling_config", "tests/test_config.py::TestSettings::test_validate_missing_feishu_config", "tests/test_config.py::TestSettings::test_validate_success", "tests/test_models.py::TestFeishuRecord::test_get_field", "tests/test_models.py::TestFeishuRecord::test_is_field_empty", "tests/test_models.py::TestGitLabMR::test_str_representation", "tests/test_models.py::TestUpdateFields::test_bool_conversion", "tests/test_models.py::TestUpdateFields::test_has_updates", "tests/test_models.py::TestUpdateFields::test_to_dict", "tests/test_models.py::TestUpdateFields::test_to_dict_partial", "tests/test_plugins.py::TestDefaultContentExtractorPlugin::test_extract_content", "tests/test_plugins.py::TestDefaultContentExtractorPlugin::test_extract_content_empty", "tests/test_plugins.py::TestGitLabURLParserPlugin::test_can_parse_gitlab_url", "tests/test_plugins.py::TestGitLabURLParserPlugin::test_parse_invalid_url", "tests/test_plugins.py::TestGitLabURLParserPlugin::test_parse_url", "tests/test_plugins.py::TestPluginManager::test_enable_disable_plugin", "tests/test_plugins.py::TestPluginManager::test_get_plugins_by_type", "tests/test_plugins.py::TestPluginManager::test_register_and_get_plugin"]