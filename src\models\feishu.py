"""飞书相关数据模型"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime


@dataclass
class FeishuRecord:
    """飞书表格记录"""
    id: str
    fields: Dict[str, Any]
    version: str
    created_time: Optional[datetime] = None
    updated_time: Optional[datetime] = None
    
    def get_field(self, field_name: str, default: Any = None) -> Any:
        """安全获取字段值"""
        return self.fields.get(field_name, default)
    
    def is_field_empty(self, field_name: str) -> bool:
        """检查字段是否为空"""
        value = self.get_field(field_name)
        if value is None:
            return True
        if isinstance(value, str) and value.strip() == "":
            return True
        if isinstance(value, list) and len(value) == 0:
            return True
        return False


@dataclass
class UpdateFields:
    """需要更新的字段"""
    commit_id: Optional[str] = None
    update_content: Optional[str] = None
    test_method: Optional[str] = None
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式"""
        fields = {}
        if self.commit_id:
            fields["Commit ID"] = self.commit_id
        if self.update_content:
            fields["更新内容"] = self.update_content
        if self.test_method:
            fields["验证要求"] = self.test_method
        return fields
    
    def has_updates(self) -> bool:
        """检查是否有需要更新的字段"""
        return bool(self.commit_id or self.update_content or self.test_method)
    
    def __bool__(self) -> bool:
        """支持布尔值判断"""
        return self.has_updates()
