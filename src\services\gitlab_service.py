"""GitLab服务"""

import re
import asyncio
from typing import Optional, List
from functools import lru_cache
import aiohttp
from urllib.parse import quote

from .base import BaseService
from ..models.gitlab import GitLabMR, GitLabCommit, GitLabMergeRequest
from ..config import Settings


class GitLabService(BaseService):
    """GitLab服务"""
    
    def __init__(self, settings: Settings):
        super().__init__(settings)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 预编译正则表达式
        self.url_patterns = [
            re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)"),
            re.compile(r"https?://[^/]+/([^/]+/[^/]+)/(merge_requests|merge_requests)/(\d+)"),
            re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)/.*")
        ]
    
    async def initialize(self) -> None:
        """初始化HTTP会话"""
        timeout = aiohttp.ClientTimeout(total=self.settings.gitlab.timeout)
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            headers={"PRIVATE-TOKEN": self.settings.gitlab.token}
        )
        self._log_info("GitLab服务初始化成功")
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.session:
            await self.session.close()
        self._log_info("GitLab服务资源已清理")
    
    @lru_cache(maxsize=128)
    def extract_mr_params(self, url: str) -> Optional[GitLabMR]:
        """从GitLab合并请求URL中提取项目路径和合并请求ID"""
        try:
            # 首先尝试使用插件系统
            if hasattr(self, 'plugin_manager'):
                from ..plugins.processors import URLParserPlugin
                url_parsers = self.plugin_manager.get_plugins_by_type(URLParserPlugin)
                for parser in url_parsers:
                    if parser.can_parse(url):
                        result = parser.parse_url(url)
                        if result:
                            return result

            # 回退到内置解析器
            for pattern in self.url_patterns:
                match = pattern.search(url)
                if match:
                    return GitLabMR(project_path=match.group(1), mr_id=int(match.group(3)))

            self._log_debug(f"无法识别的URL格式: {url}")
            return None

        except Exception as e:
            self._log_error(f"URL解析失败: {url}", e)
            return None
    
    async def get_first_commit_id(self, project_path: str, mr_id: int) -> Optional[str]:
        """获取合并请求的第一个commit ID"""
        if not self.session:
            raise RuntimeError("GitLab服务未初始化")
        
        try:
            project_encoded = quote(project_path, safe='')
            api_url = f"{self.settings.gitlab.url}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}/commits"
            
            async with self.session.get(api_url, params={"per_page": 1}) as response:
                if response.status != 200:
                    self._log_error(f"GitLab请求失败: {response.status}")
                    return None
                
                commits = await response.json()
                return commits[0]['id'] if commits else None
                
        except Exception as e:
            self._log_error(f"获取commit ID失败: {project_path}!{mr_id}", e)
            return None
    
    async def get_mr_description(self, project_path: str, mr_id: int) -> Optional[str]:
        """获取合并请求的描述内容"""
        if not self.session:
            raise RuntimeError("GitLab服务未初始化")
        
        try:
            project_encoded = quote(project_path, safe='')
            api_url = f"{self.settings.gitlab.url}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}"
            
            async with self.session.get(api_url) as response:
                if response.status != 200:
                    self._log_error(f"GitLab API请求失败: {response.status}")
                    return None
                
                data = await response.json()
                return data.get('description', '')
                
        except Exception as e:
            self._log_error(f"获取描述内容失败: {project_path}!{mr_id}", e)
            return None
    
    async def get_merge_request(self, project_path: str, mr_id: int) -> Optional[GitLabMergeRequest]:
        """获取完整的合并请求信息"""
        if not self.session:
            raise RuntimeError("GitLab服务未初始化")
        
        try:
            project_encoded = quote(project_path, safe='')
            
            # 并发获取MR信息和提交信息
            mr_task = self._get_mr_info(project_encoded, mr_id)
            commits_task = self._get_mr_commits(project_encoded, mr_id)
            
            mr_data, commits_data = await asyncio.gather(mr_task, commits_task)
            
            if not mr_data:
                return None
            
            # 转换提交数据
            commits = [
                GitLabCommit(
                    id=commit['id'],
                    short_id=commit['short_id'],
                    title=commit['title'],
                    message=commit['message'],
                    author_name=commit['author_name'],
                    author_email=commit['author_email'],
                    created_at=commit['created_at'],
                    web_url=commit['web_url']
                )
                for commit in commits_data or []
            ]
            
            return GitLabMergeRequest(
                id=mr_data['id'],
                iid=mr_data['iid'],
                project_id=mr_data['project_id'],
                title=mr_data['title'],
                description=mr_data['description'],
                state=mr_data['state'],
                created_at=mr_data['created_at'],
                updated_at=mr_data['updated_at'],
                merged_at=mr_data.get('merged_at'),
                closed_at=mr_data.get('closed_at'),
                author=mr_data['author'],
                assignee=mr_data.get('assignee'),
                source_branch=mr_data['source_branch'],
                target_branch=mr_data['target_branch'],
                web_url=mr_data['web_url'],
                commits=commits
            )
            
        except Exception as e:
            self._log_error(f"获取合并请求失败: {project_path}!{mr_id}", e)
            return None
    
    async def _get_mr_info(self, project_encoded: str, mr_id: int) -> Optional[dict]:
        """获取MR基本信息"""
        api_url = f"{self.settings.gitlab.url}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}"
        
        async with self.session.get(api_url) as response:
            if response.status != 200:
                return None
            return await response.json()
    
    async def _get_mr_commits(self, project_encoded: str, mr_id: int) -> Optional[List[dict]]:
        """获取MR的提交列表"""
        api_url = f"{self.settings.gitlab.url}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}/commits"
        
        async with self.session.get(api_url) as response:
            if response.status != 200:
                return None
            return await response.json()
