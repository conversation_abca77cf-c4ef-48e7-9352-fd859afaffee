"""轮询守护进程"""

import asyncio
import signal
import logging
from typing import Optional
from .container import ServiceContainer
from .processor import RecordProcessor
from ..config import Settings


class PollingDaemon:
    """轮询守护进程"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(self.__class__.__name__)
        self.running = False
        self._container: Optional[ServiceContainer] = None
        self._processor: Optional[RecordProcessor] = None
    
    async def start(self) -> None:
        """启动守护进程"""
        self.logger.info("启动飞书表格监控守护进程...")
        
        # 设置信号处理器
        self._setup_signal_handlers()
        
        # 初始化服务容器
        self._container = ServiceContainer(self.settings)
        await self._container.initialize()
        
        # 创建处理器
        self._processor = RecordProcessor(self._container)
        
        self.running = True
        self.logger.info("守护进程启动成功，开始监控飞书表格...")
        
        try:
            await self._run_polling_loop()
        except Exception as e:
            self.logger.exception(f"守护进程运行异常: {e}")
        finally:
            await self.stop()
    
    async def stop(self) -> None:
        """停止守护进程"""
        self.logger.info("正在停止守护进程...")
        self.running = False
        
        if self._container:
            await self._container.cleanup()
        
        self.logger.info("守护进程已停止")
    
    async def _run_polling_loop(self) -> None:
        """运行轮询循环"""
        retry_count = 0
        
        while self.running:
            try:
                self.logger.info(f"开始轮询表格 (轮询间隔: {self.settings.polling.interval}秒)")
                
                # 获取记录
                feishu_service = self._container.feishu_service
                records = await feishu_service.get_records(self.settings.polling.batch_size)
                
                if not records:
                    self.logger.info("没有需要处理的新记录")
                else:
                    # 处理记录
                    updates = await self._processor.process_records(records)
                    
                    if updates:
                        # 批量更新
                        update_data = [(record_id, fields.to_dict()) for record_id, fields in updates]
                        success_count = await feishu_service.batch_update_records(update_data)
                        self.logger.info(f"本次轮询更新了 {success_count} 条记录")
                    else:
                        self.logger.info("本次轮询未发现需要更新的记录")
                
                # 重置重试计数器
                retry_count = 0
                
            except Exception as e:
                self.logger.error(f"处理过程中发生错误: {e}")
                retry_count += 1
                
                if retry_count >= self.settings.polling.max_retries:
                    self.logger.error(f"达到最大重试次数 ({self.settings.polling.max_retries})，程序将退出")
                    self.running = False
                    break
                
                self.logger.info(f"将在 {self.settings.polling.retry_delay} 秒后重试 ({retry_count}/{self.settings.polling.max_retries})")
                await asyncio.sleep(self.settings.polling.retry_delay)
                continue
            
            # 等待下一次轮询
            await self._wait_for_next_poll()
    
    async def _wait_for_next_poll(self) -> None:
        """等待下一次轮询"""
        for _ in range(self.settings.polling.interval):
            if not self.running:
                break
            await asyncio.sleep(1)
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备退出...")
            self.running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Windows下的特殊处理
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, signal_handler)
