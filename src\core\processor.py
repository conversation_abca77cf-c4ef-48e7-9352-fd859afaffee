"""记录处理器"""

import asyncio
import logging
from typing import List, Tuple, Optional
from ..models.feishu import FeishuRecord, UpdateFields
from ..models.gitlab import GitLabMR
from .container import ServiceContainer


class RecordProcessor:
    """记录处理器 - 核心业务逻辑"""
    
    def __init__(self, container: ServiceContainer):
        self.container = container
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def process_record(self, record: FeishuRecord) -> UpdateFields:
        """处理单条记录"""
        update_fields = UpdateFields()
        
        # 获取版本地址
        version_url = record.get_field("版本地址", "")
        if not version_url or record.is_field_empty("版本地址"):
            self.logger.info(f"记录 {record.id} 缺少有效的版本地址")
            return update_fields
        
        # 解析MR参数
        gitlab_service = self.container.gitlab_service
        mr = gitlab_service.extract_mr_params(version_url)
        if not mr:
            self.logger.warning(f"记录 {record.id} 无法解析版本地址: {version_url}")
            return update_fields
        
        # 并发获取所需数据
        tasks = []
        
        # 如果需要更新Commit ID
        if record.is_field_empty("Commit ID"):
            tasks.append(self._get_commit_id(mr))
        else:
            tasks.append(asyncio.create_task(self._return_none()))
        
        # 如果需要更新内容字段
        if record.is_field_empty("更新内容") or record.is_field_empty("验证要求"):
            tasks.append(self._get_mr_content(mr))
        else:
            tasks.append(asyncio.create_task(self._return_none()))
        
        # 等待所有任务完成
        commit_id, content_data = await asyncio.gather(*tasks)
        
        # 处理结果
        content_processor = self.container.content_processor
        
        if commit_id:
            update_fields.commit_id = commit_id
            self.logger.info(f"准备更新Commit ID: {record.id} -> {commit_id}")
        
        if content_data:
            update_content, test_method = content_data
            
            if update_content and record.is_field_empty("更新内容"):
                update_fields.update_content = update_content
                self.logger.info(f"准备更新更新内容: {record.id}")
            
            if test_method and record.is_field_empty("验证要求"):
                update_fields.test_method = test_method
                self.logger.info(f"准备更新验证要求: {record.id}")
        
        return update_fields
    
    async def process_records(self, records: List[FeishuRecord]) -> List[Tuple[str, UpdateFields]]:
        """批量处理记录"""
        if not records:
            return []
        
        self.logger.info(f"开始处理 {len(records)} 条记录")
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(10)
        
        async def process_single(record: FeishuRecord) -> Tuple[str, UpdateFields]:
            async with semaphore:
                try:
                    update_fields = await self.process_record(record)
                    return record.id, update_fields
                except Exception as e:
                    self.logger.error(f"处理记录 {record.id} 时出错: {e}")
                    return record.id, UpdateFields()
        
        # 并发处理所有记录
        tasks = [process_single(record) for record in records]
        results = await asyncio.gather(*tasks)
        
        # 过滤出需要更新的记录
        updates = [(record_id, fields) for record_id, fields in results if fields.has_updates()]
        
        self.logger.info(f"处理完成: {len(updates)} 条记录需要更新")
        return updates
    
    async def _get_commit_id(self, mr: GitLabMR) -> Optional[str]:
        """获取提交ID"""
        try:
            gitlab_service = self.container.gitlab_service
            return await gitlab_service.get_first_commit_id(mr.project_path, mr.mr_id)
        except Exception as e:
            self.logger.error(f"获取提交ID失败: {mr}", e)
            return None
    
    async def _get_mr_content(self, mr: GitLabMR) -> Optional[Tuple[Optional[str], Optional[str]]]:
        """获取MR内容"""
        try:
            gitlab_service = self.container.gitlab_service
            content_processor = self.container.content_processor
            
            description = await gitlab_service.get_mr_description(mr.project_path, mr.mr_id)
            if not description:
                return None
            
            return content_processor.process_mr_content(description)
        except Exception as e:
            self.logger.error(f"获取MR内容失败: {mr}", e)
            return None
    
    async def _return_none(self) -> None:
        """返回None的辅助方法"""
        return None
