"""数据模型测试"""

import pytest
from src.models.feishu import <PERSON>ishuRecord, UpdateFields
from src.models.gitlab import GitLabMR


class TestFeishuRecord:
    """飞书记录测试"""
    
    def test_get_field(self):
        """测试获取字段值"""
        record = FeishuRecord(
            id="test_id",
            fields={"field1": "value1", "field2": ""},
            version="v1"
        )
        
        assert record.get_field("field1") == "value1"
        assert record.get_field("field2") == ""
        assert record.get_field("nonexistent") is None
        assert record.get_field("nonexistent", "default") == "default"
    
    def test_is_field_empty(self):
        """测试字段是否为空"""
        record = FeishuRecord(
            id="test_id",
            fields={
                "empty_string": "",
                "whitespace": "   ",
                "none_value": None,
                "empty_list": [],
                "valid_value": "content"
            },
            version="v1"
        )
        
        assert record.is_field_empty("empty_string") is True
        assert record.is_field_empty("whitespace") is True
        assert record.is_field_empty("none_value") is True
        assert record.is_field_empty("empty_list") is True
        assert record.is_field_empty("nonexistent") is True
        assert record.is_field_empty("valid_value") is False


class TestUpdateFields:
    """更新字段测试"""
    
    def test_to_dict(self):
        """测试转换为字典"""
        fields = UpdateFields(
            commit_id="abc123",
            update_content="更新内容",
            test_method="测试方法"
        )
        
        result = fields.to_dict()
        expected = {
            "Commit ID": "abc123",
            "更新内容": "更新内容",
            "验证要求": "测试方法"
        }
        
        assert result == expected
    
    def test_to_dict_partial(self):
        """测试部分字段转换"""
        fields = UpdateFields(commit_id="abc123")
        
        result = fields.to_dict()
        expected = {"Commit ID": "abc123"}
        
        assert result == expected
    
    def test_has_updates(self):
        """测试是否有更新"""
        # 有更新
        fields1 = UpdateFields(commit_id="abc123")
        assert fields1.has_updates() is True
        
        # 无更新
        fields2 = UpdateFields()
        assert fields2.has_updates() is False
    
    def test_bool_conversion(self):
        """测试布尔值转换"""
        fields1 = UpdateFields(commit_id="abc123")
        assert bool(fields1) is True
        
        fields2 = UpdateFields()
        assert bool(fields2) is False


class TestGitLabMR:
    """GitLab MR测试"""
    
    def test_str_representation(self):
        """测试字符串表示"""
        mr = GitLabMR(project_path="group/project", mr_id=123)
        assert str(mr) == "group/project!123"
