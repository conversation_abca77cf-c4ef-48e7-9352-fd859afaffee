"""服务基类"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Optional
from ..config import Settings


class BaseService(ABC):
    """服务基类"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass
    
    def _log_error(self, message: str, exception: Optional[Exception] = None) -> None:
        """记录错误日志"""
        if exception:
            self.logger.exception(f"{message}: {exception}")
        else:
            self.logger.error(message)
    
    def _log_info(self, message: str) -> None:
        """记录信息日志"""
        self.logger.info(message)
    
    def _log_debug(self, message: str) -> None:
        """记录调试日志"""
        self.logger.debug(message)
